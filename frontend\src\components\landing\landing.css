/* Landing Page Custom Styles */

/* Glass effect */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Brutalist shadow effect */
.brutalist-shadow {
  box-shadow: 8px 8px 0px 0px rgba(0, 0, 0, 0.1);
}

.brutalist-shadow:hover {
  box-shadow: none;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)), hsl(var(--accent)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Floating animation */
.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Pulse glow effect */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from {
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3);
  }
  to {
    box-shadow: 0 0 40px rgba(var(--primary-rgb), 0.6);
  }
}

/* Hover lift effect */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Stagger animation for children */
.stagger-children > * {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-children > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-children > *:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Magnetic hover effect */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic:hover {
  transform: scale(1.05);
}

/* Shimmer effect */
.shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Typewriter effect */
.typewriter {
  overflow: hidden;
  border-right: 2px solid hsl(var(--primary));
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: hsl(var(--primary));
  }
}

/* Parallax scroll effect */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

/* Neon glow */
.neon-glow {
  text-shadow: 
    0 0 5px hsl(var(--primary)),
    0 0 10px hsl(var(--primary)),
    0 0 15px hsl(var(--primary)),
    0 0 20px hsl(var(--primary));
}

/* Morphing background */
.morphing-bg {
  background: linear-gradient(-45deg, 
    hsl(var(--primary)), 
    hsl(var(--secondary)), 
    hsl(var(--accent)), 
    hsl(var(--primary))
  );
  background-size: 400% 400%;
  animation: gradient-morph 15s ease infinite;
}

@keyframes gradient-morph {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .brutalist-shadow {
    box-shadow: 4px 4px 0px 0px rgba(0, 0, 0, 0.1);
  }
  
  .floating {
    animation-duration: 4s;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .brutalist-shadow {
    box-shadow: 8px 8px 0px 0px rgba(255, 255, 255, 0.1);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .floating,
  .gradient-shift,
  .pulse-glow,
  .shimmer,
  .gradient-morph {
    animation: none;
  }
  
  .hover-lift:hover {
    transform: none;
  }
}
